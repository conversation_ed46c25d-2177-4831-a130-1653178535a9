# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess

def run_step2_precision_optimized(dataset='meld', model_type='bilstm', use_audio=True, use_visual=True,
                                 use_emotion_category=True, batch_size=128, epochs=25, learning_rate=0.001,
                                 step1_pred_dir=None):
    """运行精确率优化的Step2训练"""
    cmd = [
        sys.executable, 'train_step2.py',
        '--dataset', dataset,
        '--model_type', model_type,
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate),
        '--weight_decay', '2e-4',  # 增加正则化
        '--dropout', '0.3',        # 增加dropout
        '--patience', '8',         # 减少patience，避免过拟合
        '--gradient_clip', '0.5',  # 更严格的梯度裁剪
        '--negative_sampling_ratio', '0.5',  # 减少负采样比例，平衡数据
        '--save_dir', './checkpoints/step2_precision_optimized',
        '--log_dir', './logs/step2_precision_optimized'
    ]

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emotion_category:
        cmd.append('--use_emotion_category')

    # 如果提供了Step1预测目录，则使用预测结果
    if step1_pred_dir and os.path.exists(step1_pred_dir):
        cmd.extend(['--use_predicted_labels', '--step1_pred_dir', step1_pred_dir])
        print(f"✓ 使用Step1预测结果: {step1_pred_dir}")
    else:
        print("⚠️  未使用Step1预测结果，将使用所有话语对作为候选")

    print(f"运行精确率优化Step2命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Step2训练失败!")
        return False
    print("✓ Step2训练完成")
    return True

def run_step2_focal_loss(dataset='meld', model_type='bilstm', use_audio=True, use_visual=True,
                        use_emotion_category=True, batch_size=128, epochs=25, learning_rate=0.001,
                        step1_pred_dir=None):
    """使用Focal Loss的Step2训练"""
    cmd = [
        sys.executable, 'train_step2_focal.py',  # 需要创建这个文件
        '--dataset', dataset,
        '--model_type', model_type,
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate),
        '--weight_decay', '1e-4',
        '--dropout', '0.25',
        '--patience', '8',
        '--gradient_clip', '1.0',
        '--focal_alpha', '0.75',   # Focal Loss参数
        '--focal_gamma', '2.0',    # Focal Loss参数
        '--save_dir', './checkpoints/step2_focal',
        '--log_dir', './logs/step2_focal'
    ]

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emotion_category:
        cmd.append('--use_emotion_category')

    if step1_pred_dir and os.path.exists(step1_pred_dir):
        cmd.extend(['--use_predicted_labels', '--step1_pred_dir', step1_pred_dir])

    print(f"运行Focal Loss Step2命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Focal Loss Step2训练失败!")
        return False
    print("✓ Focal Loss Step2训练完成")
    return True

def run_step2_threshold_tuning(dataset='meld', model_type='bilstm', step1_pred_dir=None):
    """运行阈值调优"""
    cmd = [
        sys.executable, 'tune_threshold.py',  # 需要创建这个文件
        '--dataset', dataset,
        '--model_type', model_type,
        '--model_path', './checkpoints/step2_optimized/best_model.pt',
        '--step1_pred_dir', step1_pred_dir if step1_pred_dir else '',
        '--output_dir', './threshold_results'
    ]

    print(f"运行阈值调优命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ 阈值调优失败!")
        return False
    print("✓ 阈值调优完成")
    return True

def main():
    parser = argparse.ArgumentParser(description='MECPE PyTorch 精确率优化训练脚本')
    parser.add_argument('--strategy', type=str, 
                       choices=['precision_opt', 'focal_loss', 'threshold_tune', 'all'],
                       default='all', help='优化策略')
    parser.add_argument('--dataset', type=str, choices=['iemocap', 'meld'],
                       default='meld', help='数据集')
    parser.add_argument('--model_type', type=str, choices=['bilstm', 'bert'],
                       default='bilstm', help='模型类型')
    parser.add_argument('--use_audio', action='store_true', default=True,
                       help='使用音频特征')
    parser.add_argument('--use_visual', action='store_true', default=True,
                       help='使用视觉特征')
    parser.add_argument('--use_emotion_category', action='store_true', default=True,
                       help='使用情感类别（Step2）')
    parser.add_argument('--step1_pred_dir', type=str, 
                       default='./step1_predictions/meld_bilstm_optimized',
                       help='Step1预测结果目录')

    args = parser.parse_args()

    print("=== MECPE PyTorch 精确率优化训练流程 ===")
    print("精确率优化策略:")
    print("1. 增加正则化 (dropout=0.3, weight_decay=2e-4)")
    print("2. 减少负采样比例，平衡正负样本")
    print("3. 使用Focal Loss处理类别不平衡")
    print("4. 阈值调优找到最佳决策边界")
    print("5. 更严格的梯度裁剪防止过拟合")

    print(f"数据集: {args.dataset}")
    print(f"模型类型: {args.model_type}")
    print(f"优化策略: {args.strategy}")

    if args.strategy in ['precision_opt', 'all']:
        print("\n=== 策略1: 精确率优化训练 ===")
        success = run_step2_precision_optimized(
            args.dataset, args.model_type, args.use_audio, args.use_visual,
            args.use_emotion_category, batch_size=128, epochs=25, learning_rate=0.001,
            step1_pred_dir=args.step1_pred_dir
        )
        if not success:
            print("❌ 精确率优化训练失败")
            return

    if args.strategy in ['focal_loss', 'all']:
        print("\n=== 策略2: Focal Loss训练 ===")
        print("注意: 需要先创建train_step2_focal.py文件")
        # success = run_step2_focal_loss(
        #     args.dataset, args.model_type, args.use_audio, args.use_visual,
        #     args.use_emotion_category, batch_size=128, epochs=25, learning_rate=0.001,
        #     step1_pred_dir=args.step1_pred_dir
        # )

    if args.strategy in ['threshold_tune', 'all']:
        print("\n=== 策略3: 阈值调优 ===")
        print("注意: 需要先创建tune_threshold.py文件")
        # success = run_step2_threshold_tuning(
        #     args.dataset, args.model_type, args.step1_pred_dir
        # )

    print("\n=== 🎯 精确率优化建议 ===")
    print("1. 手动调整类别权重:")
    print("   - 减少正类权重: 从3.367降到2.0-2.5")
    print("   - 或使用平衡权重: [1.0, 2.0]")
    
    print("\n2. 阈值调优:")
    print("   - 当前默认阈值0.5可能过低")
    print("   - 建议尝试0.6-0.8的阈值")
    print("   - 使用验证集找最佳F1阈值")
    
    print("\n3. 特征工程:")
    print("   - 增加距离特征权重")
    print("   - 添加话语长度、位置等特征")
    print("   - 考虑情感强度特征")
    
    print("\n4. 模型架构:")
    print("   - 增加分类器层数")
    print("   - 使用注意力机制")
    print("   - 尝试对比学习")
    
    print("\n5. 数据策略:")
    print("   - 困难负样本挖掘")
    print("   - 数据增强")
    print("   - 交叉验证")

if __name__ == "__main__":
    main()
