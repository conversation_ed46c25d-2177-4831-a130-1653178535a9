# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess

from model_path_utils import get_roberta_model_path

def run_bert_step1_optimized(dataset='meld', use_audio=True, use_visual=True,
                            batch_size=16, epochs=25, learning_rate=2e-5, use_emocate=True):
    """运行优化的BERT Step1训练"""
    cmd = [
        sys.executable, 'train_step1.py',
        '--dataset', dataset,
        '--model_type', 'bert',
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate),
        '--weight_decay', '1e-4',
        '--dropout', '0.1',  # BERT用较小的dropout
        '--patience', '8',
        '--gradient_clip', '1.0',
        '--bert_encoding_type', 'bert_doc',  # 使用bert_doc获得更好的上下文
        '--warmup_steps', '200',
        '--save_dir', './checkpoints/step1_bert_optimized',
        '--log_dir', './logs/step1_bert_optimized'
    ]

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emocate:
        cmd.append('--use_emocate')

    print(f"运行优化BERT Step1命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ BERT Step1训练失败!")
        return False
    print("✓ BERT Step1训练完成")
    return True

def generate_bert_step1_predictions_optimized(dataset='meld', use_emocate=True):
    """生成优化的BERT Step1预测结果"""
    # 确定Step1模型路径
    base_dir = './checkpoints/step1_bert_optimized'
    best_path = os.path.join(base_dir, 'best_model.pt')
    step1_model_path = None
    if os.path.exists(best_path):
        step1_model_path = best_path
    else:
        # 回退到最近的checkpoint_epoch_*.pt
        if os.path.exists(base_dir):
            candidates = [f for f in os.listdir(base_dir) if f.startswith('checkpoint_epoch_') and f.endswith('.pt')]
            if candidates:
                # 按 epoch 值排序，取最新
                candidates.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
                step1_model_path = os.path.join(base_dir, candidates[-1])
    if not step1_model_path:
        print(f"❌ 找不到BERT Step1模型: {best_path} 或任何 checkpoint_epoch_*.pt")
        return False
    print(f"✓ 使用BERT Step1模型: {step1_model_path}")

    # 设置预测结果输出目录
    pred_dir = f"./step1_predictions/{dataset}_bert_optimized"
    os.makedirs(pred_dir, exist_ok=True)

    cmd = [
        sys.executable, 'generate_step1_predictions.py',
        '--step1_model_path', step1_model_path,
        '--dataset', dataset,
        '--model_type', 'bert',
        '--output_dir', pred_dir,
        '--batch_size', '8',  # BERT预测时用较小batch size
        '--bert_encoding_type', 'bert_doc'
    ]

    if use_emocate:
        cmd.append('--use_emocate')

    print(f"运行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ BERT Step1预测生成失败!")
        return False
    print("✓ BERT Step1预测结果生成完成")
    return pred_dir

def run_bert_step2_optimized(dataset='meld', use_audio=True, use_visual=True,
                            use_emotion_category=True, batch_size=32, epochs=20, 
                            learning_rate=1e-5, step1_pred_dir=None):
    """运行优化的BERT Step2训练"""
    cmd = [
        sys.executable, 'train_step2.py',
        '--dataset', dataset,
        '--model_type', 'bert',
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate),
        '--weight_decay', '1e-4',
        '--dropout', '0.1',  # BERT用较小的dropout
        '--patience', '8',
        '--gradient_clip', '1.0',
        '--warmup_steps', '100',
        '--save_dir', './checkpoints/step2_bert_optimized',
        '--log_dir', './logs/step2_bert_optimized'
    ]

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emotion_category:
        cmd.append('--use_emotion_category')

    # 如果提供了Step1预测目录，则使用预测结果
    if step1_pred_dir and os.path.exists(step1_pred_dir):
        cmd.extend(['--use_predicted_labels', '--step1_pred_dir', step1_pred_dir])
        print(f"✓ 使用BERT Step1预测结果: {step1_pred_dir}")
    else:
        print("⚠️  未使用Step1预测结果，将使用所有话语对作为候选")

    print(f"运行优化BERT Step2命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ BERT Step2训练失败!")
        return False
    print("✓ BERT Step2训练完成")
    return True

def main():
    parser = argparse.ArgumentParser(description='MECPE PyTorch BERT优化训练脚本')
    parser.add_argument('--step', type=str, choices=['step1', 'step2', 'both'],
                       default='both', help='运行哪个步骤')
    parser.add_argument('--dataset', type=str, choices=['iemocap', 'meld'],
                       default='meld', help='数据集')
    parser.add_argument('--use_audio', action='store_true', default=True,
                       help='使用音频特征')
    parser.add_argument('--use_visual', action='store_true', default=True,
                       help='使用视觉特征')
    parser.add_argument('--use_emotion_category', action='store_true', default=True,
                       help='使用情感类别（Step2）')
    parser.add_argument('--use_emocate', action='store_true', default=True,
                       help='使用细粒度情感分类（多类），否则使用二分类（neutral vs non-neutral）')
    parser.add_argument('--skip_step1_prediction', action='store_true', default=False,
                       help='跳过Step1预测生成（如果已存在）')

    args = parser.parse_args()

    print("=== MECPE PyTorch BERT优化训练流程 ===")
    print("BERT优化策略:")
    print("- 使用bert_doc编码方式获得更好的对话上下文")
    print("- 较小的dropout(0.1)适合预训练模型")
    print("- 添加warmup_steps进行学习率预热")
    print("- 适中的batch_size平衡性能和内存")
    print("- 较小的学习率避免破坏预训练权重")

    # 检查本地模型路径
    model_path = get_roberta_model_path()
    print(f"本地模型路径: {model_path}")
    if not os.path.exists(model_path):
        print("❌ 错误: 找不到本地RoBERTa模型!")
        print("请确保roberta文件夹位于MECPE_pytorch的同级目录下")
        return
    else:
        print("✓ 本地模型路径检查通过")

    print(f"数据集: {args.dataset}")
    print(f"使用音频: {args.use_audio}")
    print(f"使用视觉: {args.use_visual}")
    print(f"使用情感类别: {args.use_emotion_category}")
    print(f"使用细粒度情感分类: {args.use_emocate}")

    step1_pred_dir = None

    # Step1: 情感和原因检测
    if args.step in ['step1', 'both']:
        print("\n=== 第一阶段: 优化的BERT情感和原因检测 ===")
        success = run_bert_step1_optimized(args.dataset, args.use_audio, args.use_visual,
                                          batch_size=16, epochs=25, learning_rate=2e-5, 
                                          use_emocate=args.use_emocate)

        if not success:
            print("❌ BERT Step1训练失败，终止流程")
            return

    # 生成Step1预测结果（如果需要进行Step2）
    if args.step in ['step2', 'both']:
        print("\n=== 生成优化的BERT Step1预测结果 ===")

        # 检查是否已存在预测结果
        potential_pred_dir = f"./step1_predictions/{args.dataset}_bert_optimized"
        if args.skip_step1_prediction and os.path.exists(potential_pred_dir):
            print(f"✓ 跳过预测生成，使用已存在的结果: {potential_pred_dir}")
            step1_pred_dir = potential_pred_dir
        else:
            step1_pred_dir = generate_bert_step1_predictions_optimized(args.dataset, args.use_emocate)
            if not step1_pred_dir:
                print("❌ BERT Step1预测生成失败，Step2将使用所有话语对作为候选")
                step1_pred_dir = None

    # Step2: 情感-原因对提取
    if args.step in ['step2', 'both']:
        print("\n=== 第二阶段: 优化的BERT情感-原因对提取 ===")
        success = run_bert_step2_optimized(args.dataset, args.use_audio, args.use_visual,
                                          args.use_emotion_category, batch_size=32, epochs=20, 
                                          learning_rate=1e-5, step1_pred_dir=step1_pred_dir)

        if not success:
            print("❌ BERT Step2训练失败")
            return

    print("\n=== 🎉 BERT优化训练流程完成! ===")
    if step1_pred_dir:
        print(f"Step1预测结果保存在: {step1_pred_dir}")
    print("模型检查点保存在: ./checkpoints/")
    print("训练日志保存在: ./logs/")
    print("\n建议:")
    print("1. 比较BERT和BiLSTM的性能")
    print("2. 尝试不同的bert_encoding_type (bert_sen vs bert_doc)")
    print("3. 调整多模态特征权重")

if __name__ == "__main__":
    main()
