# -*- coding: utf-8 -*-

import torch
import torch.nn.functional as F
import numpy as np
import argparse
import os
import json
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.metrics import precision_recall_curve, roc_curve, auc

from config import Config, get_model_config
from data_loader import create_data_loaders
from models import MECPE_Step2_Model
from utils import set_seed, MetricsCalculator

def load_model(model_path, config, device):
    """加载训练好的模型"""
    model = MECPE_Step2_Model(config).to(device)
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    return model

def evaluate_with_threshold(model, dataloader, device, threshold=0.5):
    """使用指定阈值评估模型"""
    all_predictions = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc=f"Evaluating (threshold={threshold:.2f})"):
            # 移动数据到设备
            emo_input_ids = batch['emo_input_ids'].to(device)
            emo_attention_mask = batch['emo_attention_mask'].to(device)
            cause_input_ids = batch['cause_input_ids'].to(device)
            cause_attention_mask = batch['cause_attention_mask'].to(device)
            emo_audio = batch['emo_audio'].to(device)
            emo_visual = batch['emo_visual'].to(device)
            cause_audio = batch['cause_audio'].to(device)
            cause_visual = batch['cause_visual'].to(device)
            distance = batch['distance'].to(device)
            emotion_category = batch['emotion_category'].to(device)
            labels = batch['label'].to(device)
            
            # 前向传播
            outputs = model(
                emo_input_ids, emo_attention_mask,
                cause_input_ids, cause_attention_mask,
                emo_audio, emo_visual, cause_audio, cause_visual,
                distance, emotion_category
            )
            
            # 获取概率和预测
            logits = outputs['logits']
            probs = F.softmax(logits, dim=-1)
            pos_probs = probs[:, 1]  # 正类概率
            predictions = (pos_probs > threshold).long()
            
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(pos_probs.cpu().numpy())
    
    # 计算指标
    all_predictions = np.array(all_predictions)
    all_labels = np.array(all_labels)
    all_probs = np.array(all_probs)
    
    precision, recall, f1 = MetricsCalculator.calculate_prf(
        torch.tensor(all_predictions), torch.tensor(all_labels)
    )
    
    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'predictions': all_predictions,
        'labels': all_labels,
        'probs': all_probs
    }

def find_optimal_threshold(model, dataloader, device, thresholds=None):
    """寻找最优阈值"""
    if thresholds is None:
        thresholds = np.arange(0.1, 0.95, 0.05)
    
    results = []
    
    print("寻找最优阈值...")
    for threshold in tqdm(thresholds):
        result = evaluate_with_threshold(model, dataloader, device, threshold)
        result['threshold'] = threshold
        results.append(result)
        
        print(f"阈值 {threshold:.2f}: P={result['precision']:.4f}, "
              f"R={result['recall']:.4f}, F1={result['f1']:.4f}")
    
    # 找到最佳F1阈值
    best_f1_idx = np.argmax([r['f1'] for r in results])
    best_f1_result = results[best_f1_idx]
    
    # 找到最佳精确率阈值（在召回率>0.7的条件下）
    high_recall_results = [r for r in results if r['recall'] > 0.7]
    if high_recall_results:
        best_precision_idx = np.argmax([r['precision'] for r in high_recall_results])
        best_precision_result = high_recall_results[best_precision_idx]
    else:
        best_precision_result = results[np.argmax([r['precision'] for r in results])]
    
    return results, best_f1_result, best_precision_result

def plot_threshold_curves(results, output_dir):
    """绘制阈值曲线"""
    thresholds = [r['threshold'] for r in results]
    precisions = [r['precision'] for r in results]
    recalls = [r['recall'] for r in results]
    f1s = [r['f1'] for r in results]
    
    plt.figure(figsize=(12, 4))
    
    # 精确率-召回率-F1曲线
    plt.subplot(1, 2, 1)
    plt.plot(thresholds, precisions, 'b-', label='Precision', linewidth=2)
    plt.plot(thresholds, recalls, 'r-', label='Recall', linewidth=2)
    plt.plot(thresholds, f1s, 'g-', label='F1-Score', linewidth=2)
    plt.xlabel('Threshold')
    plt.ylabel('Score')
    plt.title('Precision, Recall, F1 vs Threshold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # PR曲线
    plt.subplot(1, 2, 2)
    plt.plot(recalls, precisions, 'b-', linewidth=2)
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curve')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'threshold_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()

def analyze_predictions(best_f1_result, best_precision_result, output_dir):
    """分析预测结果"""
    analysis = {
        'best_f1_threshold': {
            'threshold': best_f1_result['threshold'],
            'precision': best_f1_result['precision'],
            'recall': best_f1_result['recall'],
            'f1': best_f1_result['f1']
        },
        'best_precision_threshold': {
            'threshold': best_precision_result['threshold'],
            'precision': best_precision_result['precision'],
            'recall': best_precision_result['recall'],
            'f1': best_precision_result['f1']
        }
    }
    
    # 保存分析结果
    with open(os.path.join(output_dir, 'threshold_analysis.json'), 'w') as f:
        json.dump(analysis, f, indent=2)
    
    print("\n=== 阈值分析结果 ===")
    print(f"最佳F1阈值: {best_f1_result['threshold']:.2f}")
    print(f"  - 精确率: {best_f1_result['precision']:.4f}")
    print(f"  - 召回率: {best_f1_result['recall']:.4f}")
    print(f"  - F1分数: {best_f1_result['f1']:.4f}")
    
    print(f"\n最佳精确率阈值: {best_precision_result['threshold']:.2f}")
    print(f"  - 精确率: {best_precision_result['precision']:.4f}")
    print(f"  - 召回率: {best_precision_result['recall']:.4f}")
    print(f"  - F1分数: {best_precision_result['f1']:.4f}")
    
    return analysis

def main():
    parser = argparse.ArgumentParser(description='Step2模型阈值调优')
    parser.add_argument('--dataset', type=str, default='meld', choices=['iemocap', 'meld'])
    parser.add_argument('--model_type', type=str, default='bilstm', choices=['bilstm', 'bert'])
    parser.add_argument('--model_path', type=str, required=True, help='训练好的模型路径')
    parser.add_argument('--step1_pred_dir', type=str, default=None, help='Step1预测结果目录')
    parser.add_argument('--output_dir', type=str, default='./threshold_results', help='结果输出目录')
    parser.add_argument('--batch_size', type=int, default=64, help='批次大小')
    parser.add_argument('--device', type=str, default='cuda', help='设备')
    
    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(42)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建配置
    config_args = argparse.Namespace(
        dataset=args.dataset,
        model_type=args.model_type,
        use_audio=True,
        use_visual=True,
        use_emotion_category=True
    )
    
    # 获取模型配置
    config = get_model_config(config_args, stage='step2')
    
    # 加载数据
    print("加载数据...")
    _, test_loader, dev_loader, _ = create_data_loaders(
        dataset_name=args.dataset,
        batch_size=args.batch_size,
        stage='step2',
        use_predicted_labels=True if args.step1_pred_dir else False,
        step1_pred_dir=args.step1_pred_dir,
        use_emocate=True,
        use_emotion_category=True
    )
    
    # 使用验证集进行阈值调优（如果有的话）
    eval_loader = dev_loader if dev_loader is not None else test_loader
    
    # 加载模型
    print(f"加载模型: {args.model_path}")
    model = load_model(args.model_path, config, device)
    
    # 寻找最优阈值
    thresholds = np.arange(0.1, 0.9, 0.02)  # 更细粒度的阈值搜索
    results, best_f1_result, best_precision_result = find_optimal_threshold(
        model, eval_loader, device, thresholds
    )
    
    # 绘制曲线
    plot_threshold_curves(results, args.output_dir)
    
    # 分析结果
    analysis = analyze_predictions(best_f1_result, best_precision_result, args.output_dir)
    
    # 在测试集上验证最佳阈值
    if dev_loader is not None:
        print("\n=== 在测试集上验证最佳阈值 ===")
        
        # 验证最佳F1阈值
        test_f1_result = evaluate_with_threshold(
            model, test_loader, device, best_f1_result['threshold']
        )
        print(f"测试集 (F1最佳阈值 {best_f1_result['threshold']:.2f}):")
        print(f"  - 精确率: {test_f1_result['precision']:.4f}")
        print(f"  - 召回率: {test_f1_result['recall']:.4f}")
        print(f"  - F1分数: {test_f1_result['f1']:.4f}")
        
        # 验证最佳精确率阈值
        test_precision_result = evaluate_with_threshold(
            model, test_loader, device, best_precision_result['threshold']
        )
        print(f"\n测试集 (精确率最佳阈值 {best_precision_result['threshold']:.2f}):")
        print(f"  - 精确率: {test_precision_result['precision']:.4f}")
        print(f"  - 召回率: {test_precision_result['recall']:.4f}")
        print(f"  - F1分数: {test_precision_result['f1']:.4f}")
    
    print(f"\n结果已保存到: {args.output_dir}")
    print("建议:")
    print(f"1. 如果追求平衡性能，使用阈值 {best_f1_result['threshold']:.2f}")
    print(f"2. 如果追求高精确率，使用阈值 {best_precision_result['threshold']:.2f}")

if __name__ == "__main__":
    main()
