# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess

def run_step2_with_weight_strategy(dataset='meld', model_type='bilstm', 
                                  weight_strategy='conservative',
                                  use_audio=True, use_visual=True,
                                  use_emotion_category=True, 
                                  batch_size=128, epochs=15, learning_rate=0.002,
                                  step1_pred_dir=None):
    """使用指定权重策略运行Step2训练"""
    
    # 创建专门的保存目录
    save_dir = f'./checkpoints/step2_{weight_strategy}'
    log_dir = f'./logs/step2_{weight_strategy}'
    
    cmd = [
        sys.executable, 'train_step2_weighted.py',  # 需要创建这个文件
        '--dataset', dataset,
        '--model_type', model_type,
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate),
        '--weight_decay', '1e-4',
        '--dropout', '0.2',
        '--patience', '8',
        '--gradient_clip', '1.0',
        '--weight_strategy', weight_strategy,
        '--save_dir', save_dir,
        '--log_dir', log_dir
    ]

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emotion_category:
        cmd.append('--use_emotion_category')

    if step1_pred_dir and os.path.exists(step1_pred_dir):
        cmd.extend(['--use_predicted_labels', '--step1_pred_dir', step1_pred_dir])

    print(f"运行权重策略 {weight_strategy} 的Step2命令:")
    print(f"{' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print(f"❌ 权重策略 {weight_strategy} 训练失败!")
        return False
    print(f"✓ 权重策略 {weight_strategy} 训练完成")
    return True

def main():
    parser = argparse.ArgumentParser(description='测试不同权重策略的Step2训练')
    parser.add_argument('--dataset', type=str, default='meld', choices=['iemocap', 'meld'])
    parser.add_argument('--model_type', type=str, default='bilstm', choices=['bilstm', 'bert'])
    parser.add_argument('--step1_pred_dir', type=str, 
                       default='./step1_predictions/meld_bilstm_optimized',
                       help='Step1预测结果目录')
    parser.add_argument('--strategies', type=str, nargs='+',
                       default=['conservative', 'moderate', 'precision_focused'],
                       choices=['balanced', 'moderate', 'conservative', 'precision_focused', 'none'],
                       help='要测试的权重策略')

    args = parser.parse_args()

    print("=== 权重策略对比实验 ===")
    print("将测试以下权重策略:")
    for strategy in args.strategies:
        if strategy == 'balanced':
            print(f"  - {strategy}: 标准平衡权重 (原始方法)")
        elif strategy == 'moderate':
            print(f"  - {strategy}: 温和权重 (正类权重≤3.0)")
        elif strategy == 'conservative':
            print(f"  - {strategy}: 保守权重 (正类权重≤2.0)")
        elif strategy == 'precision_focused':
            print(f"  - {strategy}: 精确率导向 (正类权重=1.5)")
        elif strategy == 'none':
            print(f"  - {strategy}: 无权重 (1.0, 1.0)")

    print(f"\n数据集: {args.dataset}")
    print(f"模型类型: {args.model_type}")
    print(f"Step1预测目录: {args.step1_pred_dir}")

    results = {}
    
    for strategy in args.strategies:
        print(f"\n{'='*50}")
        print(f"测试权重策略: {strategy}")
        print(f"{'='*50}")
        
        success = run_step2_with_weight_strategy(
            dataset=args.dataset,
            model_type=args.model_type,
            weight_strategy=strategy,
            use_audio=True,
            use_visual=True,
            use_emotion_category=True,
            batch_size=128,
            epochs=15,  # 减少轮数以快速测试
            learning_rate=0.002,
            step1_pred_dir=args.step1_pred_dir
        )
        
        results[strategy] = success

    print(f"\n{'='*50}")
    print("实验结果总结:")
    print(f"{'='*50}")
    for strategy, success in results.items():
        status = "✓ 成功" if success else "❌ 失败"
        print(f"{strategy:20s}: {status}")

    print(f"\n建议:")
    print("1. 检查各策略的训练日志，比较精确率和召回率")
    print("2. 选择精确率最高且F1合理的策略")
    print("3. 可以进一步调整权重参数")
    print("4. 考虑使用阈值调优进一步优化")

if __name__ == "__main__":
    main()
